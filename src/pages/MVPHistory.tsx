import React, { Suspense, useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/custom-card";
import { useMVPVotingAdmin } from "@/hooks/useMVPVotingAdmin";
import LoadingSpinner from "@/components/LoadingSpinner";
import {
  ArrowLeft,
  Trophy,
  Calendar,
  Search,
  ChevronDown,
  ChevronUp,
  Users,
  SlidersHorizontal,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
} from "lucide-react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { PlayerName } from "@/components/player-profile/PlayerName";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { CustomPagination } from "@/components/ui/custom-pagination";
import { MobileLayout } from "@/components/layout/MobileLayout";

// Save view mode preference to localStorage
const saveViewModePreference = (mode: "cards" | "list") => {
  localStorage.setItem("mvpHistoryViewMode", mode);
};

export default function MVPHistory() {
  // Get saved view mode from localStorage or default to cards
  const [viewMode, setViewMode] = useState<"cards" | "list">(() => {
    const stored = localStorage.getItem("mvpHistoryViewMode");
    return stored === "list" ? "list" : "cards";
  });
  const itemsPerPage = viewMode === "cards" ? 8 : 10;
  const { votingPeriods, isLoading } = useMVPVotingAdmin();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [currentPage, setCurrentPage] = useState(1);

  // Update localStorage when view mode changes
  const handleViewModeChange = (mode: "cards" | "list") => {
    setViewMode(mode);
    saveViewModePreference(mode);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <MobileLayout>
          <main className="flex-1 container mx-auto px-4 py-8 max-w-screen-xl flex justify-center items-center">
            <div className="text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-4 text-muted-foreground">Lade MVP-Daten...</p>
            </div>
          </main>
          <Footer />
        </MobileLayout>
      </div>
    );
  }

  const closedVotings = votingPeriods.filter((period) => !period.is_open);

  // Filter by search term
  const filteredVotings = closedVotings.filter((period) => {
    // Prüfe, ob es MVP-Winner gibt
    if (!period.mvp_winners || period.mvp_winners.length === 0) return true;

    // Suche in allen MVP-Winner-Namen
    const hasMatchingPlayer = period.mvp_winners.some((winner) =>
      winner.player_name.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const date = period.game_sessions.date
      ? new Date(period.game_sessions.date).toLocaleDateString("de-DE")
      : "";

    return hasMatchingPlayer || date.includes(searchTerm);
  });

  // Sort by date
  const sortedVotings = [...filteredVotings].sort((a, b) => {
    const dateA = a.game_sessions.date ? new Date(a.game_sessions.date).getTime() : 0;
    const dateB = b.game_sessions.date ? new Date(b.game_sessions.date).getTime() : 0;

    return sortOrder === "desc" ? dateB - dateA : dateA - dateB;
  });

  // Pagination
  const totalPages = Math.ceil(sortedVotings.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = sortedVotings.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleNavigateToMatch = (gameSessionId: string) => {
    navigate(`/history?gameId=${gameSessionId}`);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header removeBoxShadow={true} />

      <MobileLayout>
        <main className="flex-1 px-4">
          <div className="space-y-6 max-w-screen-xl mx-auto pt-6">
            <div className="flex">
              <Button
                variant="ghost"
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 -ml-2"
                onClick={() => navigate(-1)}
              >
                <ChevronLeft className="mr-1 h-4 w-4" />
                Zurück
              </Button>
            </div>

            <Card className="w-full dark:bg-zinc-900 dark:border-zinc-800">
              <CardHeader>
                <div className="space-y-1">
                  <CardTitle className="text-3xl">MVP - Hall of Fame</CardTitle>
                  <CardDescription>Die wertvollsten Spieler aus vergangenen Spielen</CardDescription>
                </div>
                <Separator className="dark:bg-zinc-800" />
              </CardHeader>
              <CardContent className="pt-3">
                <div className="flex flex-col sm:flex-row items-center gap-4 mb-6">
                  <div className="relative w-full sm:max-w-xs">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Suche nach MVP oder Datum..."
                      className="pl-9"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <div className="flex items-center gap-2 ml-auto">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon" className="h-10 w-10">
                          <SlidersHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setSortOrder("desc")}>
                          Neueste zuerst
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setSortOrder("asc")}>
                          Älteste zuerst
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-10 w-10 ${viewMode === "cards" ? "bg-muted" : ""}`}
                      onClick={() => handleViewModeChange("cards")}
                      title="Karten-Ansicht"
                    >
                      <div className="grid grid-cols-2 gap-0.5">
                        <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                        <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                        <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                        <div className="w-1.5 h-1.5 rounded-sm bg-foreground"></div>
                      </div>
                    </Button>

                    <Button
                      variant="outline"
                      size="icon"
                      className={`h-10 w-10 ${viewMode === "list" ? "bg-muted" : ""}`}
                      onClick={() => handleViewModeChange("list")}
                      title="Listen-Ansicht"
                    >
                      <div className="flex flex-col gap-0.5 items-center justify-center">
                        <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                        <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                        <div className="w-4 h-1 rounded-sm bg-foreground"></div>
                      </div>
                    </Button>
                  </div>
                </div>

                {sortedVotings.length === 0 ? (
                  <div className="text-center py-16">
                    <Trophy className="h-16 w-16 mx-auto mb-4 text-blue-500/30 dark:text-blue-700/30" />
                    <h3 className="text-xl font-medium mb-2 dark:text-white">
                      Keine MVP-Auszeichnungen gefunden
                    </h3>
                    {searchTerm ? (
                      <p className="text-muted-foreground dark:text-zinc-400">
                        Keine Ergebnisse für "{searchTerm}". Versuche einen anderen Suchbegriff.
                      </p>
                    ) : (
                      <p className="text-muted-foreground dark:text-zinc-400">
                        Noch keine MVP-Auszeichnungen vergeben.
                      </p>
                    )}
                  </div>
                ) : viewMode === "cards" ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {currentItems.map((period) => (
                      <Card
                        key={period.id}
                        className="overflow-hidden transition-all duration-200 hover:shadow-md dark:bg-zinc-900 dark:border-zinc-800 flex flex-col"
                      >
                        <CardHeader>
                          <div className="space-y-1">
                            <div className="flex justify-between items-start">
                              <Badge
                                variant="secondary"
                                className="bg-blue-100 text-blue-500 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50"
                              >
                                <Calendar className="h-3 w-3 mr-1" />
                                {period.game_sessions.date
                                  ? new Date(period.game_sessions.date).toLocaleDateString("de-DE")
                                  : "Unbekannt"}
                              </Badge>
                            </div>
                          </div>
                          <Separator className="dark:bg-zinc-800" />
                        </CardHeader>
                        <CardContent className="pt-3">
                          {period.mvp_winners && period.mvp_winners.length > 0 ? (
                            <div className="flex flex-col items-center text-center p-2">
                              {period.mvp_winners.map((winner, index) => (
                                <div key={winner.player_id} className="mb-4 relative">
                                  <div className="flex justify-center">
                                    <div className="relative">
                                      <Avatar className="h-20 w-20 bg-gradient-to-br from-blue-400 to-blue-600 border-4 border-blue-100 dark:border-blue-900/30 shadow-lg">
                                        <AvatarFallback className="text-xl font-bold text-gray-800 dark:text-gray-200">
                                          {winner.jersey_number
                                            ? "#" + winner.jersey_number
                                            : winner.player_name.substring(0, 2).toUpperCase()}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="absolute -top-1 -right-1 bg-blue-100 dark:bg-blue-900/50 rounded-full p-0.5">
                                        <Trophy className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                                      </div>
                                    </div>
                                  </div>
                                  <h3 className="font-semibold text-lg mt-2 dark:text-white">
                                    <PlayerName
                                      playerId={winner.player_id}
                                      playerName={winner.player_name}
                                      className="hover:text-blue-700 dark:hover:text-blue-400"
                                    />
                                  </h3>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="flex flex-col items-center justify-center text-center h-full p-4 flex-1">
                              <Trophy className="h-6 w-6 text-blue-500 dark:text-blue-700 mb-2" />
                              <p className="text-muted-foreground dark:text-zinc-400">
                                Kein MVP ausgezeichnet
                              </p>
                            </div>
                          )}
                        </CardContent>
                        <CardFooter className="p-3 border-t bg-muted/30 dark:border-zinc-800 dark:bg-zinc-800/30 mt-auto">
                          <Button
                            variant="ghost"
                            className="w-full text-sm text-muted-foreground hover:text-foreground dark:text-zinc-400 dark:hover:text-white"
                            onClick={() => handleNavigateToMatch(period.game_session_id)}
                          >
                            <Users className="h-4 w-4 mr-2" />
                            Zum Spiel
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Card className="dark:bg-zinc-900 dark:border-zinc-800">
                    <CardHeader>
                      <div className="space-y-1">
                        <CardTitle>MVP Liste</CardTitle>
                        <CardDescription>
                          Chronologische Übersicht aller MVP-Auszeichnungen
                        </CardDescription>
                      </div>
                      <Separator className="dark:bg-zinc-800" />
                    </CardHeader>
                    <CardContent className="pt-3 p-0">
                      <div className="divide-y dark:divide-zinc-800">
                        {currentItems.map((period) => (
                          <div
                            key={period.id}
                            className="flex items-center justify-between p-4 hover:bg-muted/50 dark:hover:bg-zinc-800/70 transition-colors cursor-pointer"
                            onClick={() => handleNavigateToMatch(period.game_session_id)}
                          >
                            <div className="flex items-center gap-4">
                              <div className="flex-shrink-0">
                                <div className="relative">
                                  {period.mvp_winners && period.mvp_winners.length > 0 ? (
                                    <>
                                      <Avatar className="h-12 w-12 bg-gradient-to-br from-blue-400 to-blue-600">
                                        <AvatarFallback className="text-gray-800 dark:text-gray-200">
                                          {period.mvp_winners[0].jersey_number
                                            ? "#" + period.mvp_winners[0].jersey_number
                                            : period.mvp_winners[0].player_name
                                              .substring(0, 2)
                                              .toUpperCase()}
                                        </AvatarFallback>
                                      </Avatar>
                                      <div className="absolute -top-1 -right-1 bg-blue-100 dark:bg-blue-900/50 rounded-full p-0.5">
                                        <Trophy className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                                      </div>
                                    </>
                                  ) : (
                                    <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center dark:bg-zinc-800">
                                      <Trophy className="h-6 w-6 text-muted-foreground/40 dark:text-zinc-600" />
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="min-w-0">
                                <div className="flex items-center gap-2">
                                  <Badge
                                    variant="outline"
                                    className="font-normal bg-background dark:bg-zinc-800"
                                  >
                                    <Calendar className="h-3 w-3 mr-1" />
                                    {period.game_sessions.date
                                      ? new Date(period.game_sessions.date).toLocaleDateString(
                                        "de-DE"
                                      )
                                      : "Unbekannt"}
                                  </Badge>
                                </div>

                                {period.mvp_winners && period.mvp_winners.length > 0 ? (
                                  <div className="mt-1">
                                    <p className="font-medium dark:text-white">
                                      {period.mvp_winners.length > 1 ? "MVPs: " : "MVP: "}
                                      {period.mvp_winners.map((winner, index) => (
                                        <span key={winner.player_id}>
                                          <PlayerName
                                            playerId={winner.player_id}
                                            playerName={winner.player_name}
                                            className="hover:text-blue-700 dark:hover:text-blue-400"
                                          />
                                          {index < period.mvp_winners.length - 1 && ", "}
                                        </span>
                                      ))}
                                    </p>
                                  </div>
                                ) : (
                                  <div className="mt-1 flex items-center">
                                    <Trophy className="h-4 w-4 text-blue-500/30 dark:text-blue-700/30 mr-1.5" />
                                    <p className="text-muted-foreground dark:text-zinc-400">
                                      Kein MVP ausgezeichnet
                                    </p>
                                  </div>
                                )}
                              </div>
                            </div>

                            <Button variant="ghost" size="sm" className="gap-1">
                              Zum Spiel
                              <ArrowLeft className="h-3.5 w-3.5 rotate-180" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="border-t border-gray-100 dark:border-zinc-800 mt-6 pt-6">
                    <CustomPagination
                      currentPage={currentPage}
                      totalPages={totalPages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>

        <Footer />
      </MobileLayout>
    </div>
  );
}
