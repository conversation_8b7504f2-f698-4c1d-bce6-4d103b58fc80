import { lazy, Suspense, useState } from "react";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useGameSessions } from "@/hooks/useGameSessions";
import LoadingSpinner from "@/components/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { MobileLayout } from "@/components/layout/MobileLayout";

// Lazy load components
const MatchHistory = lazy(() => import("@/components/history/MatchHistory"));

export default function History() {
  // Only fetch past sessions for history page, no need for current session
  const { loading } = useGameSessions({
    fetchCurrent: false,
    fetchPast: true,
  });
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col">
      <Header removeBoxShadow={true} />

      <MobileLayout>
        <main className="flex-1 px-4">
          <div className="space-y-6 max-w-screen-xl mx-auto pt-6">
            <div className="flex">
              <Button
                variant="ghost"
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 -ml-2"
                onClick={() => navigate(-1)}
              >
                <ChevronLeft className="mr-1 h-4 w-4" />
                Zurück
              </Button>
            </div>

            <Suspense
              fallback={
                <div className="w-full flex justify-center items-center py-12">
                  <div className="text-center">
                    <LoadingSpinner size="lg" />
                    <p className="mt-4 text-muted-foreground">Lade Spielhistorie...</p>
                  </div>
                </div>
              }
            >
              <MatchHistory />
            </Suspense>
          </div>
        </main>

        <Footer />
      </MobileLayout>
    </div>
  );
}
