import { cn } from "@/lib/utils";
import { Menu } from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { HeaderMenu } from "@/components/navigation/HeaderMenu";
import { useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useIsMobile } from "@/hooks/use-mobile";

interface HeaderProps {
  className?: string;
  showSubtitle?: boolean;
  removeBoxShadow?: boolean;
}

export default function Header({
  className,
  showSubtitle = true,
  removeBoxShadow = false,
}: HeaderProps) {
  const location = useLocation();
  const isMobile = useIsMobile();
  const activePage = getActivePageFromPath(location.pathname);
  const isHomePage = location.pathname === "/";

  // Check if we're in test environment
  // Production domains: internrw.de, www.internrw.de
  // Test domains: test.internrw.de, preview deployments, local development
  const isTestEnvironment =
    typeof window !== "undefined" &&
    ![
      "internrw.de",
      "www.internrw.de",
      "inter-nrw-kickoff-crew.vercel.app",
      "inter-nrw-kickoff-crew-git-main-muammercakirs-projects.vercel.app",
      "inter-nrw-kickoff-crew-muammercakirs-projects.vercel.app",
    ].includes(window.location.hostname);

  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn");
    window.location.href = "/login";
  };

  // Function to determine active page from pathname
  function getActivePageFromPath(pathname: string): string {
    if (pathname === "/") return "home";
    if (pathname.startsWith("/history")) return "history";
    if (pathname.startsWith("/admin")) return "admin";
    if (pathname.startsWith("/mvp-history")) return "mvp-history";
    if (pathname.startsWith("/statistics")) return "statistics";
    if (pathname.startsWith("/feedback")) return "feedback";
    return "";
  }

  const headerClasses = cn(
    "py-4 px-4",
    isHomePage
      ? "bg-transparent"
      : "bg-team-secondary dark:bg-zinc-900 dark:border-b dark:border-zinc-800",
    !isHomePage && !removeBoxShadow && "shadow-sm",
    className
  );

  return (
    <header className={headerClasses}>
      <div className="flex justify-between items-center max-w-screen-xl mx-auto mb-2 mt-2">
        <div className="flex items-center gap-2">
          <img
            src="/images/logo.png"
            alt="INTER NRW Logo"
            className="h-10 w-10 transition-transform hover:scale-105"
          />
          <div>
            <h1 className="text-xl font-bold tracking-tight font-poppins text-black dark:text-white flex items-center gap-2">
              INTER NRW
              {isTestEnvironment && (
                <span className="text-xs font-medium bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 px-2 py-0.5 rounded-full">
                  TEST
                </span>
              )}
            </h1>
            {showSubtitle && (
              <p className="text-xs text-muted-foreground">Weniger WhatsApp. Mehr Fußball.</p>
            )}
          </div>
        </div>

        <div className="flex items-center">
          {/* Hide hamburger menu on mobile devices (except admin/login pages) where tab bar is shown */}
          {(!isMobile || location.pathname.startsWith("/admin") || location.pathname.startsWith("/login")) && (
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="hover:bg-gray-100 dark:hover:bg-zinc-800"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-[300px] sm:w-[400px] p-0 flex flex-col dark:border-zinc-800"
              >
                <div className="p-4 border-b">
                  <h2 className="font-semibold text-lg">Navigation</h2>
                </div>
                <div className="flex-1 overflow-y-auto">
                  <HeaderMenu onLogout={handleLogout} activePage={activePage} />
                </div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </div>
    </header>
  );
}
