import React from "react";
import { useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";

interface MobileLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function MobileLayout({ children, className }: MobileLayoutProps) {
  const location = useLocation();
  const isMobile = useIsMobile();

  // Add bottom padding on mobile devices (except admin/login pages) where tab bar is shown
  const shouldAddBottomPadding = 
    isMobile && 
    !location.pathname.startsWith("/admin") && 
    !location.pathname.startsWith("/login");

  return (
    <div className={cn(
      shouldAddBottomPadding && "pb-20", // Add padding for tab bar height
      className
    )}>
      {children}
    </div>
  );
}
