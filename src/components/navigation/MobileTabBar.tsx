import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Home,
  History,
  BarChart,
  Award,
} from "lucide-react";

interface TabItem {
  id: string;
  icon: React.ReactNode;
  label: string;
  path: string;
}

const tabItems: TabItem[] = [
  { id: "home", icon: <Home className="h-5 w-5" />, label: "Startseite", path: "/" },
  { id: "history", icon: <History className="h-5 w-5" />, label: "Historie", path: "/history" },
  { id: "statistics", icon: <BarChart className="h-5 w-5" />, label: "Statistiken", path: "/statistics" },
  { id: "mvp-history", icon: <Award className="h-5 w-5" />, label: "MVP", path: "/mvp-history" },
];

// Function to determine active page from pathname
function getActivePageFromPath(pathname: string): string {
  if (pathname === "/") return "home";
  if (pathname.startsWith("/history")) return "history";
  if (pathname.startsWith("/mvp-history")) return "mvp-history";
  if (pathname.startsWith("/statistics")) return "statistics";
  return "";
}

export function MobileTabBar() {
  const location = useLocation();
  const isMobile = useIsMobile();
  const activePage = getActivePageFromPath(location.pathname);

  // Don't render on desktop or on login/admin pages
  if (!isMobile || location.pathname.startsWith("/login") || location.pathname.startsWith("/admin")) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-zinc-900 border-t border-gray-200 dark:border-zinc-800 shadow-lg">
      <div className="flex items-center justify-around px-2 py-3">
        {tabItems.map((item) => {
          const isActive = activePage === item.id;

          return (
            <Link
              key={item.id}
              to={item.path}
              className={cn(
                "flex flex-col items-center justify-center min-w-0 flex-1 py-2 px-1 rounded-lg transition-all duration-200 relative",
                isActive
                  ? "text-team-primary dark:text-team-primary/90"
                  : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-800"
              )}
            >
              {/* Active indicator */}
              {isActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-team-primary dark:bg-team-primary/90 rounded-full" />
              )}

              <div className={cn(
                "flex items-center justify-center mb-1 transition-transform duration-200",
                isActive && "scale-110"
              )}>
                {item.icon}
              </div>
              <span className={cn(
                "text-xs font-medium truncate max-w-full",
                isActive && "font-semibold"
              )}>
                {item.label}
              </span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
